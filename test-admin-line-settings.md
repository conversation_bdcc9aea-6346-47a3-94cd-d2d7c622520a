# 管理員 LINE 設定修正測試指南

## 🔧 修正內容

### 1. 新增 RPC 函數
- `admin_get_line_settings`: 載入 LINE 設定（繞過 RLS）
- `admin_save_line_settings`: 儲存 LINE 設定（繞過 RLS）

### 2. 修正前端邏輯
- 使用 RPC 函數載入和儲存設定
- 添加回退機制確保相容性
- 修正 TypeScript 類型問題

## 🧪 測試步驟

### 步驟 1: 等待 Lovable.dev 同步
1. 等待 2-3 分鐘讓 lovable.dev 從 GitHub 同步最新程式碼
2. 檢查 lovable.dev 部署狀態

### 步驟 2: 測試管理員登入
1. 訪問 `https://line-beauty-appoint.lovable.app/admin/login`
2. 使用帳號密碼登入：
   - 帳號：`admin`
   - 密碼：`admin123`
3. 確認登入成功並進入管理後台

### 步驟 3: 測試 LINE 設定載入
1. 點擊 "LINE API 設定" 卡片
2. 檢查是否正確載入現有設定：
   - Channel ID: `2007619149`
   - Channel Secret: 應顯示為星號
   - Redirect URI: `https://line-beauty-appoint.lovable.app/auth/line/callback`
3. 如果設定正確載入，表示載入功能已修正

### 步驟 4: 測試 LINE 設定儲存
1. 修改任一設定值（例如在 Channel ID 後面加上 `-test`）
2. 點擊 "儲存設定" 按鈕
3. 檢查是否出現成功訊息："設定已儲存 - LINE API 設定已成功更新"
4. 重新整理頁面，確認修改已保存

### 步驟 5: 測試診斷頁面
1. 點擊 "診斷頁面" 按鈕
2. 檢查診斷結果是否顯示：
   - ✅ Channel ID: 已設定
   - ✅ Channel Secret: 已設定
   - ✅ Redirect URI: 已設定
   - ✅ LINE 設定存在

## 🔍 除錯資訊

### 如果仍然出現問題：

1. **檢查瀏覽器控制台**：
   - 按 F12 開啟開發者工具
   - 查看 Console 標籤是否有錯誤訊息
   - 查找 "RPC 載入結果" 或 "LINE 設定儲存成功" 訊息

2. **檢查網路請求**：
   - 在 Network 標籤中查看 API 請求
   - 確認 RPC 函數調用是否成功

3. **手動測試 RPC 函數**：
   ```sql
   -- 測試載入函數
   SELECT public.admin_get_line_settings('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'::uuid);
   
   -- 測試儲存函數
   SELECT public.admin_save_line_settings(
     'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'::uuid,
     'test-channel-id',
     'test-channel-secret',
     'https://line-beauty-appoint.lovable.app/auth/line/callback',
     '',
     '',
     ''
   );
   ```

## 📝 預期結果

修正後應該能夠：
- ✅ 正常載入現有的 LINE 設定
- ✅ 成功儲存修改的設定
- ✅ 診斷頁面顯示正確狀態
- ✅ 不再出現 "LINE 設定不存在" 錯誤

## 🚨 如果問題持續

如果修正後仍有問題，可能需要：
1. 檢查 lovable.dev 的環境變數設定
2. 確認 Supabase 專案連接正確
3. 檢查 RLS 政策是否有其他限制
4. 聯繫 lovable.dev 技術支援
