import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowLeft, Shield, Eye, EyeOff } from "lucide-react";
import { useAdminAuth } from "@/contexts/AdminAuthContext";
import { toast } from "@/hooks/use-toast";

const AdminLogin = () => {
  const navigate = useNavigate();
  const { adminLogin } = useAdminAuth();
  
  const [formData, setFormData] = useState({
    username: "",
    password: ""
  });
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.username || !formData.password) {
      toast({
        title: "請填寫完整資訊",
        description: "請輸入帳號和密碼",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    
    try {
      const success = await adminLogin(formData.username, formData.password);
      
      if (success) {
        toast({
          title: "登入成功",
          description: "歡迎進入管理後台",
        });
        navigate("/admin");
      } else {
        toast({
          title: "登入失敗",
          description: "帳號或密碼錯誤",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('管理員登入錯誤:', error);
      toast({
        title: "登入失敗",
        description: "系統錯誤，請稍後再試",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center mb-4">
            <Button 
              variant="ghost" 
              size="icon"
              onClick={() => navigate("/")}
              className="absolute left-4 top-4"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
          </div>
          <div className="w-16 h-16 mx-auto bg-slate-800 rounded-full flex items-center justify-center">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-slate-800">管理員登入</h1>
          <p className="text-slate-600">請輸入管理員帳號密碼</p>
        </div>

        {/* Login Form */}
        <Card className="p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">帳號</Label>
              <Input
                id="username"
                name="username"
                type="text"
                value={formData.username}
                onChange={handleInputChange}
                placeholder="請輸入管理員帳號"
                disabled={loading}
                className="h-12"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">密碼</Label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="請輸入密碼"
                  disabled={loading}
                  className="h-12 pr-12"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-12 w-12"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full h-12 bg-slate-800 hover:bg-slate-700 text-white"
              disabled={loading}
            >
              {loading ? "登入中..." : "登入"}
            </Button>
          </form>
        </Card>

        {/* Demo Info */}
        <Card className="p-4 bg-blue-50 border-blue-200">
          <div className="text-center space-y-2">
            <h3 className="font-medium text-blue-800">測試帳號</h3>
            <div className="text-sm text-blue-600 space-y-1">
              <p><strong>帳號：</strong>admin</p>
              <p><strong>密碼：</strong>admin123</p>
            </div>
          </div>
        </Card>

        {/* Footer */}
        <div className="text-center">
          <p className="text-xs text-slate-500">
            管理後台僅供授權人員使用
          </p>
        </div>
      </div>
    </div>
  );
};

export default AdminLogin;
