import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Search, Eye, Edit, Trash2, UserPlus, LogOut } from "lucide-react";
import { useAdminAuth } from "@/contexts/AdminAuthContext";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

interface Member {
  id: string;
  user_id: string;
  display_name: string | null;
  email: string | null;
  phone: string | null;
  avatar_url: string | null;
  created_at: string;
  booking_count?: number;
  total_spent?: number;
}

const MemberManagement = () => {
  const navigate = useNavigate();
  const { adminLogout } = useAdminAuth();

  const handleLogout = () => {
    adminLogout();
    navigate("/admin/login");
  };
  const [searchTerm, setSearchTerm] = useState("");
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingMember, setEditingMember] = useState<Member | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editForm, setEditForm] = useState({
    display_name: "",
    email: "",
    phone: ""
  });

  // 載入會員資料
  useEffect(() => {
    fetchMembers();
  }, []);

  const fetchMembers = async () => {
    setLoading(true);
    try {
      // 載入所有 profiles
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (profilesError) throw profilesError;

      // 為每個會員計算預約次數和消費金額
      const membersWithStats = await Promise.all(
        (profiles || []).map(async (profile) => {
          // 計算預約次數
          const { count: bookingCount } = await supabase
            .from('bookings')
            .select('*', { count: 'exact' })
            .eq('user_id', profile.user_id);

          // 計算總消費金額
          const { data: bookings } = await supabase
            .from('bookings')
            .select('estimated_price')
            .eq('user_id', profile.user_id)
            .eq('status', 'completed');

          const totalSpent = bookings?.reduce((sum, booking) => 
            sum + (booking.estimated_price || 0), 0) || 0;

          return {
            ...profile,
            booking_count: bookingCount || 0,
            total_spent: totalSpent
          };
        })
      );

      setMembers(membersWithStats);
    } catch (error: any) {
      toast({
        title: "載入會員資料失敗",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredMembers = members.filter(member =>
    (member.display_name?.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (member.email?.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (member.phone?.includes(searchTerm))
  );

  const handleEditMember = (member: Member) => {
    setEditingMember(member);
    setEditForm({
      display_name: member.display_name || "",
      email: member.email || "",
      phone: member.phone || ""
    });
    setIsEditDialogOpen(true);
  };

  const handleSaveMember = async () => {
    if (!editingMember) return;

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          display_name: editForm.display_name,
          email: editForm.email,
          phone: editForm.phone,
          updated_at: new Date().toISOString()
        })
        .eq('id', editingMember.id);

      if (error) throw error;

      toast({
        title: "更新成功",
        description: "會員資料已更新",
      });

      setIsEditDialogOpen(false);
      setEditingMember(null);
      fetchMembers();
    } catch (error: any) {
      console.error('更新會員資料錯誤:', error);
      toast({
        title: "更新失敗",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleDeleteMember = async (memberId: string) => {
    if (!confirm('確定要刪除此會員嗎？此操作無法復原。')) {
      return;
    }

    try {
      // 注意：這裡應該軟刪除而不是硬刪除，以保留歷史記錄
      const { error } = await supabase
        .from('profiles')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', memberId);

      if (error) throw error;

      toast({
        title: "會員已停用",
        description: "會員已被停用，但保留歷史記錄",
      });

      // 重新載入會員列表
      fetchMembers();
    } catch (error: any) {
      console.error('停用會員錯誤:', error);
      toast({
        title: "操作失敗",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-TW');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-rose-100 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => navigate('/admin')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              返回後台
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">會員管理</h1>
              <p className="text-gray-600">查看與管理所有會員資料</p>
            </div>
          </div>
          <Button
            variant="outline"
            onClick={handleLogout}
            className="flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50"
          >
            <LogOut className="h-4 w-4" />
            登出
          </Button>
        </div>

        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>會員列表 ({filteredMembers.length} 位會員)</CardTitle>
              <Button 
                className="flex items-center gap-2"
                onClick={() => toast({
                  title: "功能開發中",
                  description: "新增會員功能即將推出",
                })}
              >
                <UserPlus className="h-4 w-4" />
                新增會員
              </Button>
            </div>
            <div className="flex items-center gap-4 mt-4">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="搜尋會員姓名、信箱或電話..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">載入中...</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>會員</TableHead>
                    <TableHead>聯絡資訊</TableHead>
                    <TableHead>加入日期</TableHead>
                    <TableHead>預約次數</TableHead>
                    <TableHead>消費金額</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMembers.map((member) => (
                    <TableRow key={member.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarImage src={member.avatar_url || ""} />
                            <AvatarFallback>
                              {member.display_name?.charAt(0) || member.email?.charAt(0) || 'U'}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">
                              {member.display_name || member.email || '未設定姓名'}
                            </div>
                            <div className="text-sm text-gray-500">ID: {member.user_id}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="text-sm">{member.email || '未設定'}</div>
                          <div className="text-sm text-gray-500">{member.phone || '未設定'}</div>
                        </div>
                      </TableCell>
                      <TableCell>{formatDate(member.created_at)}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{member.booking_count || 0} 次</Badge>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">NT$ {(member.total_spent || 0).toLocaleString()}</span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => toast({
                              title: "功能開發中",
                              description: "會員詳情功能即將推出",
                            })}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditMember(member)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="text-red-500 hover:text-red-700"
                            onClick={() => handleDeleteMember(member.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}

            {!loading && filteredMembers.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                {searchTerm ? '沒有找到符合條件的會員' : '尚無會員資料'}
              </div>
            )}
          </CardContent>
        </Card>

        {/* 編輯會員對話框 */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>編輯會員資料</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="display_name">姓名</Label>
                <Input
                  id="display_name"
                  value={editForm.display_name}
                  onChange={(e) => setEditForm({ ...editForm, display_name: e.target.value })}
                  placeholder="請輸入姓名"
                />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={editForm.email}
                  onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                  placeholder="請輸入 Email"
                />
              </div>
              <div>
                <Label htmlFor="phone">電話</Label>
                <Input
                  id="phone"
                  value={editForm.phone}
                  onChange={(e) => setEditForm({ ...editForm, phone: e.target.value })}
                  placeholder="請輸入電話號碼"
                />
              </div>
              <div className="flex gap-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                  className="flex-1"
                >
                  取消
                </Button>
                <Button
                  onClick={handleSaveMember}
                  className="flex-1"
                >
                  儲存
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default MemberManagement;