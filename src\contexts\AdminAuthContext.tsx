import React, { createContext, useContext, useState, useEffect } from 'react';

interface AdminAuthContextType {
  isAdminAuthenticated: boolean;
  adminLogin: (username: string, password: string) => Promise<boolean>;
  adminLogout: () => void;
  loading: boolean;
}

const AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined);

export const useAdminAuth = () => {
  const context = useContext(AdminAuthContext);
  if (context === undefined) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  return context;
};

interface AdminAuthProviderProps {
  children: React.ReactNode;
}

export const AdminAuthProvider: React.FC<AdminAuthProviderProps> = ({ children }) => {
  const [isAdminAuthenticated, setIsAdminAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  // 管理員認證資訊
  const ADMIN_CREDENTIALS = {
    username: 'admin',
    password: 'admin123'
  };

  // 檢查本地存儲的認證狀態
  useEffect(() => {
    const checkAuthStatus = () => {
      try {
        const adminToken = localStorage.getItem('admin_token');
        const adminExpiry = localStorage.getItem('admin_token_expiry');
        
        if (adminToken && adminExpiry) {
          const expiryTime = parseInt(adminExpiry);
          const currentTime = Date.now();
          
          if (currentTime < expiryTime) {
            setIsAdminAuthenticated(true);
          } else {
            // Token 已過期，清除
            localStorage.removeItem('admin_token');
            localStorage.removeItem('admin_token_expiry');
            setIsAdminAuthenticated(false);
          }
        } else {
          setIsAdminAuthenticated(false);
        }
      } catch (error) {
        console.error('檢查管理員認證狀態錯誤:', error);
        setIsAdminAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  const adminLogin = async (username: string, password: string): Promise<boolean> => {
    try {
      // 驗證帳號密碼
      if (username === ADMIN_CREDENTIALS.username && password === ADMIN_CREDENTIALS.password) {
        // 生成簡單的 token（實際應用中應使用更安全的方式）
        const token = btoa(`${username}:${Date.now()}`);
        const expiryTime = Date.now() + (24 * 60 * 60 * 1000); // 24小時後過期
        
        // 儲存認證資訊
        localStorage.setItem('admin_token', token);
        localStorage.setItem('admin_token_expiry', expiryTime.toString());
        
        setIsAdminAuthenticated(true);
        
        console.log('管理員登入成功');
        return true;
      } else {
        console.log('管理員登入失敗：帳號或密碼錯誤');
        return false;
      }
    } catch (error) {
      console.error('管理員登入錯誤:', error);
      return false;
    }
  };

  const adminLogout = () => {
    try {
      // 清除認證資訊
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_token_expiry');
      
      setIsAdminAuthenticated(false);
      
      console.log('管理員已登出');
    } catch (error) {
      console.error('管理員登出錯誤:', error);
    }
  };

  const value: AdminAuthContextType = {
    isAdminAuthenticated,
    adminLogin,
    adminLogout,
    loading
  };

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  );
};
