-- 創建管理員專用的 LINE 設定儲存 RPC 函數
-- 這個函數繞過 RLS 限制，允許管理員儲存 LINE 設定

CREATE OR REPLACE FUNCTION public.admin_save_line_settings(
  p_shop_id uuid,
  p_channel_id text DEFAULT NULL,
  p_channel_secret text DEFAULT NULL,
  p_redirect_uri text DEFAULT NULL,
  p_new_member_webhook text DEFAULT NULL,
  p_booking_success_webhook text DEFAULT NULL,
  p_reminder_webhook text DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result_record record;
BEGIN
  -- 記錄操作日誌
  RAISE LOG 'Admin saving LINE settings for shop_id: %', p_shop_id;
  
  -- 執行 upsert 操作（繞過 RLS）
  INSERT INTO public.line_settings (
    shop_id,
    channel_id,
    channel_secret,
    redirect_uri,
    new_member_webhook,
    booking_success_webhook,
    reminder_webhook,
    updated_at
  ) VALUES (
    p_shop_id,
    p_channel_id,
    p_channel_secret,
    p_redirect_uri,
    p_new_member_webhook,
    p_booking_success_webhook,
    p_reminder_webhook,
    now()
  )
  ON CONFLICT (shop_id) 
  DO UPDATE SET
    channel_id = EXCLUDED.channel_id,
    channel_secret = EXCLUDED.channel_secret,
    redirect_uri = EXCLUDED.redirect_uri,
    new_member_webhook = EXCLUDED.new_member_webhook,
    booking_success_webhook = EXCLUDED.booking_success_webhook,
    reminder_webhook = EXCLUDED.reminder_webhook,
    updated_at = now()
  RETURNING *;
  
  -- 獲取更新後的記錄
  SELECT * INTO result_record 
  FROM public.line_settings 
  WHERE shop_id = p_shop_id;
  
  -- 返回成功結果
  RETURN json_build_object(
    'success', true,
    'message', 'LINE settings saved successfully',
    'data', row_to_json(result_record)
  );
  
EXCEPTION
  WHEN OTHERS THEN
    -- 記錄錯誤
    RAISE LOG 'Error saving LINE settings: %', SQLERRM;
    
    -- 返回錯誤結果
    RETURN json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to save LINE settings'
    );
END;
$$;

-- 添加函數註釋
COMMENT ON FUNCTION public.admin_save_line_settings IS 'Admin function to save LINE settings bypassing RLS policies';

-- 授予執行權限給 authenticated 角色
GRANT EXECUTE ON FUNCTION public.admin_save_line_settings TO authenticated;
GRANT EXECUTE ON FUNCTION public.admin_save_line_settings TO anon;
