# LINE Login Token 交換錯誤除錯指南

## 🚨 錯誤現象
```
POST https://jyehyqcjusiywggofpld.supabase.co/functions/v1/line-login-token 400 (Bad Request)
Failed to exchange token: FunctionsHttpError: Edge Function returned a non-2xx status code
Token exchange details: {code: 'CTyu2wevkN...', redirectUri: 'https://line-beauty-appoint.lovable.app/auth/line/callback', channelId: '2007619149...', hasChannelSecret: false}
```

## 🔍 問題分析

### 關鍵問題：`hasChannelSecret: false`
這表示前端傳送給 Edge Function 的 `channelSecret` 參數為空或未定義。

### 🔧 已修正的問題
1. **RPC 函數修正**: `get_line_login_settings` 現在正確返回 `channel_secret`
2. **前端邏輯**: 使用 RPC 函數載入完整的 LINE 設定

## 🧪 除錯步驟

### 步驟 1: 等待同步
等待 2-3 分鐘讓 lovable.dev 同步最新的程式碼和資料庫修正。

### 步驟 2: 清除瀏覽器快取
1. 按 `Ctrl + Shift + R` (Windows) 或 `Cmd + Shift + R` (Mac) 強制重新整理
2. 或者開啟無痕模式測試

### 步驟 3: 檢查 RPC 函數
在瀏覽器控制台執行以下測試：
```javascript
// 測試 RPC 函數是否正確返回 channel_secret
const { data } = await supabase.rpc('get_line_login_settings', {
  p_shop_id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'
});
console.log('RPC 結果:', data);
```

預期結果應該包含：
```json
{
  "success": true,
  "data": {
    "channel_id": "2007619149",
    "channel_secret": "e745d6f258bc4f0890253b0268a54b6d",
    "redirect_uri": "https://line-beauty-appoint.lovable.app/auth/line/callback"
  }
}
```

### 步驟 4: 檢查前端載入邏輯
在 LINE 登入過程中，檢查瀏覽器控制台是否有以下訊息：
- `RPC error loading LINE settings:` (如果有，表示 RPC 調用失敗)
- `RPC 載入結果:` (應該顯示包含 channel_secret 的資料)

### 步驟 5: 檢查 Edge Function 參數
在 token 交換失敗時，檢查控制台的 `Token exchange details`：
- `hasChannelSecret` 應該為 `true`
- `channelId` 應該有值
- `redirectUri` 應該正確

## 🔧 手動測試 RPC 函數

如果問題持續，可以在 Supabase Dashboard 中手動測試：

```sql
-- 測試 RPC 函數
SELECT public.get_line_login_settings('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'::uuid);

-- 檢查原始資料
SELECT channel_id, channel_secret, redirect_uri 
FROM public.line_settings 
WHERE shop_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';
```

## 🚨 如果問題持續

### 可能的原因：
1. **快取問題**: lovable.dev 的快取還沒更新
2. **RPC 函數未生效**: 資料庫修正還沒同步
3. **權限問題**: RLS 政策仍然阻止存取

### 解決方案：
1. **等待更長時間**: 有時 lovable.dev 需要 5-10 分鐘同步
2. **檢查 Supabase 日誌**: 查看是否有 RPC 調用錯誤
3. **聯繫支援**: 如果問題持續超過 15 分鐘

## 📝 測試清單

- [ ] 等待 lovable.dev 同步 (2-3 分鐘)
- [ ] 清除瀏覽器快取
- [ ] 測試 RPC 函數返回正確資料
- [ ] 檢查前端控制台訊息
- [ ] 確認 `hasChannelSecret: true`
- [ ] 測試 LINE 登入流程

## ✅ 成功指標

修正成功後應該看到：
- `hasChannelSecret: true` 在 token 交換詳情中
- LINE 登入正常跳轉並完成授權
- 不再出現 400 錯誤

如果仍有問題，請提供最新的錯誤訊息和控制台輸出。
