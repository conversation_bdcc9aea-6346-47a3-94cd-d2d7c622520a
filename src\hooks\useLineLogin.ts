import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import {
  generateValidVirtualEmail,
  generateConsistentVirtualPassword,
  validateEmailFormat,
  validateLineLoginData,
  createLineUserMetadata,
  logLineLoginEvent
} from "@/utils/lineUserHelper";
import {
  emergencyStateRecovery,
  validateStateTimestamp,
  logStateValidationReport
} from "@/utils/stateValidationDiagnostic";

export const useLineLogin = () => {
  const navigate = useNavigate();
  const [error, setError] = useState<string>("");
  const [loading, setLoading] = useState(true);

  const processLineCallback = async (searchParams: URLSearchParams) => {
    try {
      const code = searchParams.get("code");
      const state = searchParams.get("state");
      const error = searchParams.get("error");
      const errorDescription = searchParams.get("error_description");

      // 檢查是否有錯誤
      if (error) {
        console.error("LINE login error:", error, errorDescription);
        setError(`LINE 登入失敗: ${errorDescription || error}`);
        return;
      }

      // 驗證 state 參數（防止 CSRF 攻擊）
      const savedState = localStorage.getItem("line_login_state");
      if (!state) {
        console.warn("No state parameter in URL");
        // 生成新的 state 並重新導向 LINE 登入
        const newState = Math.random().toString(36).substring(2, 15);
        localStorage.setItem("line_login_state", newState);
        setError("缺少安全驗證參數，請重新登入");
        return;
      }
      
      if (state !== savedState) {
        console.error("State parameter mismatch:", { received: state, saved: savedState });

        // 執行詳細的 state 驗證診斷
        logStateValidationReport();

        // 嘗試緊急恢復機制
        if (!savedState && state) {
          console.log("🚨 嘗試緊急 state 恢復...");

          // 檢查 state 的時間有效性
          if (validateStateTimestamp(state)) {
            console.log("✅ State 時間戳驗證通過");

            // 嘗試緊急恢復
            if (emergencyStateRecovery(state)) {
              console.log("✅ 緊急恢復成功，繼續登入流程");
              toast({
                title: "已自動修復登入問題",
                description: "系統已自動恢復登入狀態",
              });
              // 繼續執行，不返回
            } else {
              console.error("❌ 緊急恢復失敗");
              setError("安全驗證失敗，請重新登入");
              return;
            }
          } else {
            console.error("❌ State 已過期或無效");
            setError("登入連結已過期，請重新登入");
            return;
          }
        } else {
          setError("安全驗證失敗，請重新登入");
          return;
        }
      }

      // 清除 state
      localStorage.removeItem("line_login_state");

      if (!code) {
        setError("未收到授權碼，請重新登入");
        return;
      }

      // 使用 RPC 函數獲取 LINE 設定
      const { data: rpcResult, error: rpcError } = await supabase.rpc('get_line_login_settings', {
        p_shop_id: "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"
      });

      let lineSettings;
      if (rpcError || !(rpcResult as any)?.success) {
        console.error("RPC error loading LINE settings:", rpcError);
        // 回退到直接查詢
        const { data: fallbackSettings, error: settingsError } = await supabase
          .from("line_settings")
          .select("channel_id, channel_secret, redirect_uri")
          .eq("shop_id", "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa")
          .single();

        if (settingsError || !fallbackSettings) {
          console.error("Failed to get LINE settings:", settingsError);
          setError("無法獲取 LINE 設定，請聯繫管理員");
          return;
        }
        lineSettings = fallbackSettings;
      } else {
        lineSettings = (rpcResult as any).data;
      }

      // 使用 Supabase Edge Function 交換 code 獲取 token
      const { data: tokenData, error: tokenError } = await supabase.functions.invoke(
        "line-login-token",
        {
          body: {
            code,
            redirectUri: lineSettings.redirect_uri || `${window.location.origin}/auth/line/callback`,
            channelId: lineSettings.channel_id,
            channelSecret: lineSettings.channel_secret,
          },
        }
      );

      if (tokenError || !tokenData) {
        console.error("Failed to exchange token:", tokenError);
        console.error("Token exchange details:", {
          code: code?.substring(0, 10) + "...",
          redirectUri: lineSettings.redirect_uri,
          channelId: lineSettings.channel_id?.substring(0, 10) + "...",
          hasChannelSecret: !!lineSettings.channel_secret
        });

        let errorMessage = "無法獲取 LINE 授權，請重新登入";
        if (tokenError?.message) {
          errorMessage += `\n詳細錯誤: ${tokenError.message}`;
        }

        setError(errorMessage);
        return;
      }

      // 驗證 LINE 登入資料
      const validation = validateLineLoginData(tokenData);
      if (!validation.valid) {
        console.error("LINE login data validation failed:", validation.errors);
        setError(`LINE 登入資料驗證失敗: ${validation.errors.join(', ')}`);
        return;
      }

      // 創建/登入用戶
      const lineUserId = tokenData.profile.userId;
      logLineLoginEvent(lineUserId, 'Processing authentication');
      
      const email = generateValidVirtualEmail(lineUserId);
      const password = generateConsistentVirtualPassword(lineUserId);
      
      // 驗證生成的 email 格式
      const emailValidation = validateEmailFormat(email);
      if (!emailValidation.valid) {
        console.error("Generated email validation failed:", emailValidation.errors);
        setError(`生成的 Email 無效: ${emailValidation.errors.join(', ')}`);
        return;
      }

      let authData;
      let isNewUser = false;
      try {
        // 嘗試使用虛擬 email 登入
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (signInError && signInError.message.includes('Invalid login credentials')) {
          // 用戶不存在，創建新用戶
          const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
            email,
            password,
            options: {
              data: createLineUserMetadata(tokenData.profile)
            }
          });

          if (signUpError) {
            console.error("Failed to create LINE user:", signUpError);
            setError(`創建 LINE 用戶失敗: ${signUpError.message}`);
            return;
          }
          authData = signUpData;
          isNewUser = true;
        } else if (signInError) {
          console.error("Failed to sign in LINE user:", signInError);
          setError(`LINE 用戶登入失敗: ${signInError.message}`);
          return;
        } else {
          authData = signInData;
        }
      } catch (err) {
        console.error("Unexpected auth error:", err);
        setError("認證過程發生未預期的錯誤");
        return;
      }

      // 檢查認證是否成功
      if (!authData?.user) {
        setError("登入失敗，請重試");
        return;
      }

      // 同步用戶資料到 profiles 表
      await syncUserProfile(authData.user, tokenData.profile, isNewUser);

      // 登入成功
      logLineLoginEvent(lineUserId, 'Authentication successful');
      toast({
        title: "登入成功",
        description: "歡迎使用 LINE 帳號登入",
      });
      navigate("/profile");
    } catch (err) {
      console.error("Unexpected error during LINE callback:", err);
      setError("處理 LINE 登入時發生未預期的錯誤");
    } finally {
      setLoading(false);
    }
  };

  // 同步用戶資料到 profiles 表
  const syncUserProfile = async (user: any, lineProfile: any, isNewUser: boolean) => {
    try {
      const profileData = {
        user_id: user.id,
        display_name: lineProfile.displayName,
        email: user.email,
        avatar_url: lineProfile.pictureUrl,
        updated_at: new Date().toISOString()
      };

      if (isNewUser) {
        // 新用戶：創建 profile 記錄
        const { error: insertError } = await supabase
          .from('profiles')
          .insert({
            ...profileData,
            created_at: new Date().toISOString()
          });

        if (insertError) {
          console.error("Failed to create user profile:", insertError);
          // 不拋出錯誤，避免影響登入流程
        } else {
          console.log("User profile created successfully");
        }
      } else {
        // 現有用戶：更新 profile 記錄
        const { error: updateError } = await supabase
          .from('profiles')
          .upsert(profileData, {
            onConflict: 'user_id'
          });

        if (updateError) {
          console.error("Failed to update user profile:", updateError);
          // 不拋出錯誤，避免影響登入流程
        } else {
          console.log("User profile updated successfully");
        }
      }
    } catch (error) {
      console.error("Error syncing user profile:", error);
      // 不拋出錯誤，避免影響登入流程
    }
  };

  return {
    error,
    loading,
    processLineCallback,
  };
};