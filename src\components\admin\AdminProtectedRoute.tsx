import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAdminAuth } from "@/contexts/AdminAuthContext";

interface AdminProtectedRouteProps {
  children: React.ReactNode;
}

const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({ children }) => {
  const { isAdminAuthenticated, loading } = useAdminAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading && !isAdminAuthenticated) {
      // 如果未認證，重定向到管理員登入頁面
      navigate("/admin/login", { replace: true });
    }
  }, [isAdminAuthenticated, loading, navigate]);

  // 顯示載入狀態
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-8 h-8 border-4 border-slate-300 border-t-slate-600 rounded-full animate-spin mx-auto"></div>
          <p className="text-slate-600">檢查認證狀態...</p>
        </div>
      </div>
    );
  }

  // 如果未認證，不渲染內容（會被重定向）
  if (!isAdminAuthenticated) {
    return null;
  }

  // 如果已認證，渲染子組件
  return <>{children}</>;
};

export default AdminProtectedRoute;
