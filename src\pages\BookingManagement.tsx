import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Calendar, Clock, User, MapPin, Phone, MessageCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

interface Booking {
  id: string;
  service_id: string;
  booking_date: string;
  booking_time: string;
  status: string;
  estimated_price: number | null;
  notes: string | null;
  guest_count: number;
  created_at: string;
  service: {
    name: string;
    duration: number;
    price: number;
  } | null;
  service_provider: {
    name: string;
  } | null;
}

const BookingManagement = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const [activeTab, setActiveTab] = useState("upcoming");
  const [bookings, setBookings] = useState<{
    upcoming: Booking[];
    completed: Booking[];
    cancelled: Booking[];
  }>({
    upcoming: [],
    completed: [],
    cancelled: []
  });
  const [loading, setLoading] = useState(true);

  // 載入用戶預約資料
  useEffect(() => {
    if (user) {
      loadBookings();
    }
  }, [user]);

  const loadBookings = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          *,
          service:services(name, duration, price),
          service_provider:service_providers(name)
        `)
        .eq('user_id', user.id)
        .order('booking_date', { ascending: false });

      if (error) throw error;

      // 根據狀態和日期分類預約
      const now = new Date();
      const today = now.toISOString().split('T')[0];

      const categorizedBookings = {
        upcoming: [] as Booking[],
        completed: [] as Booking[],
        cancelled: [] as Booking[]
      };

      (data || []).forEach((booking: any) => {
        const bookingDate = booking.booking_date;

        if (booking.status === 'cancelled') {
          categorizedBookings.cancelled.push(booking);
        } else if (booking.status === 'completed' || bookingDate < today) {
          categorizedBookings.completed.push(booking);
        } else {
          categorizedBookings.upcoming.push(booking);
        }
      });

      setBookings(categorizedBookings);
    } catch (error: any) {
      console.error('Error loading bookings:', error);
      toast({
        title: "載入失敗",
        description: "無法載入預約資料",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 取消預約
  const cancelBooking = async (bookingId: string) => {
    try {
      const { error } = await supabase
        .from('bookings')
        .update({ status: 'cancelled' })
        .eq('id', bookingId);

      if (error) throw error;

      toast({
        title: "預約已取消",
        description: "您的預約已成功取消",
      });

      // 重新載入資料
      loadBookings();
    } catch (error: any) {
      console.error('Error cancelling booking:', error);
      toast({
        title: "取消失敗",
        description: "無法取消預約，請稍後再試",
        variant: "destructive",
      });
    }
  };

  // 修改預約（重新導向到預約頁面）
  const modifyBooking = (booking: Booking) => {
    // 將預約資訊存到 localStorage，讓預約頁面可以預填
    localStorage.setItem('modifyBooking', JSON.stringify({
      bookingId: booking.id,
      serviceId: booking.service_id,
      date: booking.booking_date,
      time: booking.booking_time,
      guestCount: booking.guest_count,
      notes: booking.notes
    }));

    navigate('/services');
  };

  if (!user) {
    navigate('/login');
    return null;
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground">載入中...</p>
        </div>
      </div>
    );
  }

  const oldBookings = {
    upcoming: [
      {
        id: "HK202407150001",
        service: "美甲-單色、法式、貓眼、璀璨款",
        staff: "專業美甲師 Anna",
        date: "2024/07/20",
        time: "14:00",
        duration: "2小時",
        price: 1680,
        status: "confirmed",
        address: "台北市中山區建國北路二段123號"
      },
      {
        id: "HK202407160002",
        service: "臉部深層清潔保養",
        staff: "美容師 Jessica",
        date: "2024/07/25",
        time: "10:30",
        duration: "90分鐘",
        price: 2200,
        status: "confirmed",
        address: "台北市中山區建國北路二段123號"
      }
    ],
    completed: [
      {
        id: "HK202407100003",
        service: "美甲-光療指甲",
        staff: "專業美甲師 Anna",
        date: "2024/07/10",
        time: "15:00",
        duration: "2小時",
        price: 1500,
        status: "completed",
        address: "台北市中山區建國北路二段123號"
      }
    ],
    cancelled: []
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'confirmed':
        return { color: 'bg-green-100 text-green-800', text: '已確認' };
      case 'completed':
        return { color: 'bg-blue-100 text-blue-800', text: '已完成' };
      case 'cancelled':
        return { color: 'bg-red-100 text-red-800', text: '已取消' };
      default:
        return { color: 'bg-gray-100 text-gray-800', text: '未知' };
    }
  };

  const tabs = [
    { id: 'upcoming', label: '即將到來', count: bookings.upcoming.length },
    { id: 'completed', label: '已完成', count: bookings.completed.length },
    { id: 'cancelled', label: '已取消', count: bookings.cancelled.length }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-primary text-primary-foreground p-4">
        <div className="flex items-center justify-between">
          <Button 
            variant="ghost" 
            size="icon"
            className="text-primary-foreground hover:bg-primary-light"
            onClick={() => navigate("/profile")}
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <h1 className="text-lg font-semibold">預約管理</h1>
          <div className="w-10"></div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white border-b border-border">
        <div className="flex">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>
      </div>

      <div className="p-4 space-y-4">
        {bookings[activeTab as keyof typeof bookings].length > 0 ? (
          bookings[activeTab as keyof typeof bookings].map((booking) => {
            const statusInfo = getStatusInfo(booking.status);
            
            return (
              <Card key={booking.id} className="p-4 card-shadow">
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-semibold text-foreground">
                        {booking.service?.name || '未知服務'}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        預約編號: {booking.id.substring(0, 8)}...
                      </p>
                    </div>
                    <Badge className={statusInfo.color}>
                      {statusInfo.text}
                    </Badge>
                  </div>

                  {/* Staff Info */}
                  {booking.service_provider && (
                    <div className="flex items-center space-x-2">
                      <User className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm text-foreground">
                        {booking.service_provider.name}
                      </span>
                    </div>
                  )}

                  {/* Date & Time */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm text-foreground">
                        {new Date(booking.booking_date).toLocaleDateString('zh-TW')}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm text-foreground">
                        {booking.booking_time}
                        {booking.service?.duration && ` (${booking.service.duration}分鐘)`}
                      </span>
                    </div>
                  </div>

                  {/* Guest Count */}
                  {booking.guest_count > 1 && (
                    <div className="flex items-center space-x-2">
                      <User className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm text-foreground">
                        人數: {booking.guest_count}人
                      </span>
                    </div>
                  )}

                  {/* Notes */}
                  {booking.notes && (
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <p className="text-sm text-muted-foreground">備註</p>
                      <p className="text-sm text-foreground">{booking.notes}</p>
                    </div>
                  )}

                  {/* Price */}
                  <div className="bg-primary-lighter p-3 rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">服務費用</span>
                      <span className="font-semibold text-primary">
                        ${booking.estimated_price || booking.service?.price || 0}
                      </span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-2">
                    {(booking.status === 'confirmed' || booking.status === 'pending') && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1"
                          onClick={() => modifyBooking(booking)}
                        >
                          <MessageCircle className="w-4 h-4 mr-1" />
                          修改預約
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          className="flex-1"
                          onClick={() => {
                            if (window.confirm('確定要取消這個預約嗎？')) {
                              cancelBooking(booking.id);
                            }
                          }}
                        >
                          取消預約
                        </Button>
                      </>
                    )}

                    {booking.status === 'completed' && (
                      <Button size="sm" className="flex-1">
                        <MessageCircle className="w-4 h-4 mr-1" />
                        撰寫評價
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            );
          })
        ) : (
          <div className="text-center py-12">
            <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="font-medium text-foreground mb-2">沒有{tabs.find(t => t.id === activeTab)?.label}的預約</h3>
            <p className="text-muted-foreground mb-4">
              {activeTab === 'upcoming' && '您目前沒有即將到來的預約'}
              {activeTab === 'completed' && '您還沒有完成任何預約'}
              {activeTab === 'cancelled' && '您沒有取消的預約記錄'}
            </p>
            {activeTab === 'upcoming' && (
              <Button onClick={() => navigate("/services")}>
                立即預約
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default BookingManagement;