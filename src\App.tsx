import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Welcome from "./pages/Welcome";
import Login from "./pages/Login";
import LineCallback from "./pages/auth/LineCallback";
import ServiceSelection from "./pages/ServiceSelection";
import TimeSelection from "./pages/TimeSelection";
import BookingConfirm from "./pages/BookingConfirm";
import BookingSuccess from "./pages/BookingSuccess";
import MemberProfile from "./pages/MemberProfile";
import WalletBalance from "./pages/WalletBalance";
import BusinessSurvey from "./pages/BusinessSurvey";
import RewardsExchange from "./pages/RewardsExchange";
import PersonalSettings from "./pages/PersonalSettings";
import BookingManagement from "./pages/BookingManagement";
import CouponManagement from "./pages/CouponManagement";
import AdminDashboard from "./pages/AdminDashboard";
import LineSettings from "./pages/admin/LineSettings";
import LineLoginTest from "./pages/admin/LineLoginTest";
import MemberManagement from "./pages/admin/MemberManagement";
import MemberDetail from "./pages/admin/MemberDetail";
import TimeSlotManagement from "./pages/admin/TimeSlotManagement";
import StaffManagement from "./pages/admin/StaffManagement";
import CategoryManagement from "./pages/admin/CategoryManagement";
import ServiceManagement from "./pages/admin/ServiceManagement";
import AdminCouponManagement from "./pages/admin/AdminCouponManagement";
import DatabaseDebug from "./pages/admin/DatabaseDebug";
import PermissionFix from "./pages/admin/PermissionFix";
import AdminLogin from "./pages/admin/AdminLogin";
import { AuthProvider } from "./contexts/AuthContext";
import { AdminAuthProvider } from "./contexts/AdminAuthContext";
import AdminProtectedRoute from "./components/admin/AdminProtectedRoute";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <AdminAuthProvider>
            <Routes>
          <Route path="/" element={<Welcome />} />
          <Route path="/login" element={<Login />} />
          <Route path="/auth/line/callback" element={<LineCallback />} />
          <Route path="/services" element={<ServiceSelection />} />
          <Route path="/booking-time" element={<TimeSelection />} />
          <Route path="/booking-confirm" element={<BookingConfirm />} />
          <Route path="/booking-success" element={<BookingSuccess />} />
          <Route path="/profile" element={<MemberProfile />} />
          <Route path="/wallet" element={<WalletBalance />} />
          <Route path="/business-survey" element={<BusinessSurvey />} />
          <Route path="/rewards" element={<RewardsExchange />} />
          <Route path="/settings" element={<PersonalSettings />} />
          <Route path="/booking-management" element={<BookingManagement />} />
          <Route path="/coupons" element={<CouponManagement />} />
          {/* 管理員登入頁面（不需要保護） */}
          <Route path="/admin/login" element={<AdminLogin />} />

          {/* 受保護的管理員路由 */}
          <Route path="/admin" element={
            <AdminProtectedRoute>
              <AdminDashboard />
            </AdminProtectedRoute>
          } />
          <Route path="/admin/line-settings" element={
            <AdminProtectedRoute>
              <LineSettings />
            </AdminProtectedRoute>
          } />
          <Route path="/admin/line-test" element={
            <AdminProtectedRoute>
              <LineLoginTest />
            </AdminProtectedRoute>
          } />
          <Route path="/admin/members" element={
            <AdminProtectedRoute>
              <MemberManagement />
            </AdminProtectedRoute>
          } />
          <Route path="/admin/members/:userId" element={
            <AdminProtectedRoute>
              <MemberDetail />
            </AdminProtectedRoute>
          } />
          <Route path="/admin/time-slots" element={
            <AdminProtectedRoute>
              <TimeSlotManagement />
            </AdminProtectedRoute>
          } />
          <Route path="/admin/staff" element={
            <AdminProtectedRoute>
              <StaffManagement />
            </AdminProtectedRoute>
          } />
          <Route path="/admin/categories" element={
            <AdminProtectedRoute>
              <CategoryManagement />
            </AdminProtectedRoute>
          } />
          <Route path="/admin/services" element={
            <AdminProtectedRoute>
              <ServiceManagement />
            </AdminProtectedRoute>
          } />
          <Route path="/admin/coupons" element={
            <AdminProtectedRoute>
              <AdminCouponManagement />
            </AdminProtectedRoute>
          } />
          <Route path="/admin/database-debug" element={
            <AdminProtectedRoute>
              <DatabaseDebug />
            </AdminProtectedRoute>
          } />
          <Route path="/admin/permission-fix" element={
            <AdminProtectedRoute>
              <PermissionFix />
            </AdminProtectedRoute>
          } />
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </AdminAuthProvider>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
