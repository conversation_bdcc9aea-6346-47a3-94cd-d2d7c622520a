import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Save, TestTube } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { debugLineLogin, validateRedirectUri, generateTestLineLoginUrl } from "@/utils/lineLoginDebug";

const LineSettings = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [shopId, setShopId] = useState<string>("");
  
  const [settings, setSettings] = useState({
    channelId: "",
    channelSecret: "",
    redirectUri: "",
    newMemberWebhook: "",
    bookingSuccessWebhook: "",
    reminderWebhook: ""
  });

  useEffect(() => {
    loadSettings();
  }, []);

  // 自動設定正確的 redirect_uri
  const setCorrectRedirectUri = () => {
    const currentOrigin = window.location.origin;
    const correctRedirectUri = `${currentOrigin}/auth/line/callback`;
    setSettings(prev => ({
      ...prev,
      redirectUri: correctRedirectUri
    }));
    toast({
      title: "Redirect URI 已更新",
      description: `已設定為: ${correctRedirectUri}`,
    });
  };

  // 測試 LINE Login 設定
  const testLineLogin = async () => {
    if (!settings.channelId) {
      toast({
        title: "測試失敗",
        description: "請先填入 Channel ID",
        variant: "destructive"
      });
      return;
    }

    try {
      // 驗證 redirect_uri
      const validation = validateRedirectUri(settings.redirectUri);
      if (!validation.valid) {
        toast({
          title: "Redirect URI 驗證失敗",
          description: validation.errors.join(', '),
          variant: "destructive"
        });
        return;
      }

      // 生成測試 URL
      const testUrl = generateTestLineLoginUrl(settings.channelId, settings.redirectUri);

      // 顯示除錯資訊
      const debugInfo = await debugLineLogin();
      console.group('🔍 LINE Login 測試');
      console.log('測試 URL:', testUrl);
      console.log('除錯資訊:', debugInfo);
      console.groupEnd();

      toast({
        title: "測試 URL 已生成",
        description: "請檢查瀏覽器控制台查看詳細資訊，然後手動測試登入",
      });

      // 可選：直接開啟測試 URL
      if (confirm('是否要直接開啟 LINE Login 測試頁面？')) {
        window.open(testUrl, '_blank');
      }
    } catch (error) {
      console.error('測試失敗:', error);
      toast({
        title: "測試失敗",
        description: error instanceof Error ? error.message : '未知錯誤',
        variant: "destructive"
      });
    }
  };

  const loadSettings = async () => {
    try {
      // 使用測試店家 ID
      const currentShopId = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';
      setShopId(currentShopId);

      // 使用 RPC 函數載入 LINE 設定，繞過 RLS 限制
      const { data: rpcResult, error: rpcError } = await supabase.rpc('admin_get_line_settings', {
        p_shop_id: currentShopId
      });

      if (rpcError) {
        console.error('RPC error loading LINE settings:', rpcError);
        // 如果 RPC 函數不存在，回退到直接查詢
        if (rpcError.message?.includes('function') && rpcError.message?.includes('does not exist')) {
          console.log('RPC 函數不存在，嘗試直接載入...');
          await fallbackLoadSettings(currentShopId);
          return;
        }
        throw rpcError;
      }

      console.log('RPC 載入結果:', rpcResult);

      // 類型轉換 RPC 結果
      const result = rpcResult as any;
      if (result?.success && result?.data) {
        const lineSettings = result.data;
        setSettings({
          channelId: lineSettings.channel_id || "",
          channelSecret: lineSettings.channel_secret || "",
          redirectUri: lineSettings.redirect_uri || "",
          newMemberWebhook: lineSettings.new_member_webhook || "",
          bookingSuccessWebhook: lineSettings.booking_success_webhook || "",
          reminderWebhook: lineSettings.reminder_webhook || ""
        });
      } else {
        // 沒有找到設定，使用預設值
        setSettings({
          channelId: "",
          channelSecret: "",
          redirectUri: `${window.location.origin}/auth/line/callback`,
          newMemberWebhook: "",
          bookingSuccessWebhook: "",
          reminderWebhook: ""
        });
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      toast({
        title: "載入失敗",
        description: `載入設定時發生錯誤: ${error instanceof Error ? error.message : '未知錯誤'}`,
        variant: "destructive"
      });
    }
  };

  // 回退載入方法（直接查詢，可能受 RLS 限制）
  const fallbackLoadSettings = async (currentShopId: string) => {
    const { data: lineSettings, error } = await supabase
      .from('line_settings')
      .select('*')
      .eq('shop_id', currentShopId)
      .maybeSingle();

    if (error) {
      console.error('Fallback load error:', error);
      throw error;
    }

    if (lineSettings) {
      setSettings({
        channelId: lineSettings.channel_id || "",
        channelSecret: lineSettings.channel_secret || "",
        redirectUri: lineSettings.redirect_uri || "",
        newMemberWebhook: lineSettings.new_member_webhook || "",
        bookingSuccessWebhook: lineSettings.booking_success_webhook || "",
        reminderWebhook: lineSettings.reminder_webhook || ""
      });
    } else {
      // 沒有找到設定，使用預設值
      setSettings({
        channelId: "",
        channelSecret: "",
        redirectUri: `${window.location.origin}/auth/line/callback`,
        newMemberWebhook: "",
        bookingSuccessWebhook: "",
        reminderWebhook: ""
      });
    }
  };

  const handleSave = async () => {
    if (!shopId) {
      toast({
        title: "錯誤",
        description: "找不到店家資訊",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      // 使用 RPC 函數來儲存管理員的 LINE 設定，繞過 RLS 限制
      const { data, error } = await supabase.rpc('admin_save_line_settings', {
        p_shop_id: shopId,
        p_channel_id: settings.channelId,
        p_channel_secret: settings.channelSecret,
        p_redirect_uri: settings.redirectUri,
        p_new_member_webhook: settings.newMemberWebhook,
        p_booking_success_webhook: settings.bookingSuccessWebhook,
        p_reminder_webhook: settings.reminderWebhook
      });

      if (error) {
        console.error('RPC error:', error);
        // 如果 RPC 函數不存在，回退到直接操作
        if (error.message?.includes('function') && error.message?.includes('does not exist')) {
          console.log('RPC 函數不存在，嘗試直接儲存...');
          await fallbackSave();
          return;
        }
        throw error;
      }

      console.log('LINE 設定儲存成功:', data);
      toast({
        title: "設定已儲存",
        description: "LINE API 設定已成功更新",
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: "儲存失敗",
        description: `無法儲存 LINE 設定: ${error instanceof Error ? error.message : '未知錯誤'}`,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // 回退儲存方法（直接操作，可能受 RLS 限制）
  const fallbackSave = async () => {
    const { error } = await supabase
      .from('line_settings')
      .upsert({
        shop_id: shopId,
        channel_id: settings.channelId,
        channel_secret: settings.channelSecret,
        redirect_uri: settings.redirectUri,
        new_member_webhook: settings.newMemberWebhook,
        booking_success_webhook: settings.bookingSuccessWebhook,
        reminder_webhook: settings.reminderWebhook
      }, {
        onConflict: 'shop_id'
      });

    if (error) {
      console.error('Fallback save error:', error);
      throw error;
    }

    toast({
      title: "設定已儲存",
      description: "LINE API 設定已成功更新",
    });
  };

  const handleTest = (webhookType: string) => {
    toast({
      title: "測試 Webhook",
      description: `正在測試 ${webhookType} Webhook...`,
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-rose-100 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center gap-4 mb-8">
          <Button 
            variant="outline" 
            onClick={() => navigate('/admin')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            返回後台
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-800">LINE API 設定</h1>
            <p className="text-gray-600">設定 LINE Login 與 Webhook 通知</p>
          </div>
        </div>

        <div className="space-y-6">
          {/* LINE Login 設定 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                LINE Login 設定
              </CardTitle>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-semibold text-blue-800 mb-2">設定說明</h4>
                <p className="text-blue-700 text-sm mb-2">
                  如果遇到 "Invalid redirect_uri" 錯誤，請確保在 LINE Developers Console 中正確註冊 Callback URL。
                </p>
                <p className="text-blue-700 text-sm">
                  請在 LINE Developers Console 中註冊：<br/>
                  <code className="bg-blue-100 px-1 rounded">https://line-beauty-appoint.lovable.app/auth/line/callback</code><br/>
                  詳細設定步驟請參考：<a href="/docs/LINE_LOGIN_SETUP.md" target="_blank" className="underline">LINE Login 設定指南</a>
                </p>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="channelId">Channel ID</Label>
                <Input
                  id="channelId"
                  value={settings.channelId}
                  onChange={(e) => setSettings({...settings, channelId: e.target.value})}
                  placeholder="輸入 LINE Login Channel ID"
                />
              </div>
              <div>
                <Label htmlFor="channelSecret">Channel Secret</Label>
                <Input
                  id="channelSecret"
                  type="password"
                  value={settings.channelSecret}
                  onChange={(e) => setSettings({...settings, channelSecret: e.target.value})}
                  placeholder="輸入 LINE Login Channel Secret"
                />
              </div>
              <div>
                <Label htmlFor="redirectUri">Redirect URI</Label>
                <div className="flex gap-2">
                  <Input
                    id="redirectUri"
                    value={settings.redirectUri}
                    onChange={(e) => setSettings({...settings, redirectUri: e.target.value})}
                    placeholder="https://line-beauty-appoint.lovable.app/auth/line/callback"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={setCorrectRedirectUri}
                    className="whitespace-nowrap"
                  >
                    自動設定
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  請確保此 URI 已在 LINE Developers Console 中註冊
                </p>
              </div>

              {/* 測試按鈕 */}
              <div className="flex gap-2 pt-4 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={testLineLogin}
                  className="flex items-center gap-2"
                  disabled={!settings.channelId}
                >
                  <TestTube className="h-4 w-4" />
                  測試 LINE Login
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate('/admin/line-test')}
                  className="flex items-center gap-2"
                >
                  <TestTube className="h-4 w-4" />
                  診斷頁面
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Webhook 設定 */}
          <Card>
            <CardHeader>
              <CardTitle>n8n Webhook 設定</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="newMemberWebhook">新會員註冊 Webhook</Label>
                <div className="flex gap-2">
                  <Textarea
                    id="newMemberWebhook"
                    value={settings.newMemberWebhook}
                    onChange={(e) => setSettings({...settings, newMemberWebhook: e.target.value})}
                    placeholder="https://your-n8n.com/webhook/new-member"
                    className="flex-1"
                  />
                  <Button 
                    variant="outline" 
                    onClick={() => handleTest("新會員註冊")}
                    className="flex items-center gap-2"
                  >
                    <TestTube className="h-4 w-4" />
                    測試
                  </Button>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  當新使用者首次透過 LINE 登入時觸發
                </p>
              </div>

              <div>
                <Label htmlFor="bookingSuccessWebhook">預約成功 Webhook</Label>
                <div className="flex gap-2">
                  <Textarea
                    id="bookingSuccessWebhook"
                    value={settings.bookingSuccessWebhook}
                    onChange={(e) => setSettings({...settings, bookingSuccessWebhook: e.target.value})}
                    placeholder="https://your-n8n.com/webhook/booking-success"
                    className="flex-1"
                  />
                  <Button 
                    variant="outline" 
                    onClick={() => handleTest("預約成功")}
                    className="flex items-center gap-2"
                  >
                    <TestTube className="h-4 w-4" />
                    測試
                  </Button>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  當使用者完成預約時觸發
                </p>
              </div>

              <div>
                <Label htmlFor="reminderWebhook">預約提醒 Webhook</Label>
                <div className="flex gap-2">
                  <Textarea
                    id="reminderWebhook"
                    value={settings.reminderWebhook}
                    onChange={(e) => setSettings({...settings, reminderWebhook: e.target.value})}
                    placeholder="https://your-n8n.com/webhook/reminder"
                    className="flex-1"
                  />
                  <Button 
                    variant="outline" 
                    onClick={() => handleTest("預約提醒")}
                    className="flex items-center gap-2"
                  >
                    <TestTube className="h-4 w-4" />
                    測試
                  </Button>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  預約前 24 小時自動發送提醒
                </p>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button 
              onClick={handleSave} 
              className="flex items-center gap-2"
              disabled={loading}
            >
              <Save className="h-4 w-4" />
              {loading ? "儲存中..." : "儲存設定"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LineSettings;