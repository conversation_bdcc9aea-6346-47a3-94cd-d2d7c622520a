# 後台管理功能修正測試指南

## 🎯 修正內容總覽

### 問題 1: 營業時間重複鍵值錯誤
**錯誤**: `duplicate key value violates unique constraint "business_hours_shop_id_day_of_week_key"`

**修正方案**: 
- 修改時段管理頁面的營業時間更新邏輯
- 改為先刪除現有記錄，再插入新記錄的方式
- 避免 upsert 操作導致的約束衝突

### 問題 2: 後台會員管理編輯功能
**問題**: 會員資料編輯功能無法正常工作

**修正方案**:
- 修正更新邏輯，使用正確的 `user_id` 欄位
- 新增詳細的會員資訊頁面
- 實作完整的編輯和查看功能

## 🧪 測試步驟

### 測試 1: 營業時間設定功能
**路徑**: `https://line-beauty-appoint.lovable.app/admin/time-slots`

**前置條件**:
1. 使用管理員帳號登入 (admin/admin123)
2. 進入時段管理頁面

**測試步驟**:
1. ✅ 修改任一天的營業時間
2. ✅ 點擊「儲存設定」按鈕
3. ✅ 確認沒有出現重複鍵值錯誤
4. ✅ 重新整理頁面，確認設定已保存
5. ✅ 再次修改並儲存，確認可重複操作

**預期結果**:
- 不再出現 `duplicate key value violates unique constraint` 錯誤
- 營業時間設定正常保存和載入
- 可以重複修改和儲存

### 測試 2: 會員管理列表功能
**路徑**: `https://line-beauty-appoint.lovable.app/admin/members`

**測試步驟**:
1. ✅ 檢查會員列表是否正常載入
2. ✅ 測試搜尋功能
3. ✅ 點擊「眼睛」圖示查看會員詳情
4. ✅ 點擊「編輯」圖示編輯會員資料

**預期結果**:
- 會員列表正常顯示
- 搜尋功能正常工作
- 查看和編輯按鈕正常響應

### 測試 3: 會員詳細資訊頁面
**路徑**: `https://line-beauty-appoint.lovable.app/admin/members/{user_id}`

**測試步驟**:
1. ✅ 從會員列表點擊「眼睛」圖示進入詳情頁
2. ✅ 檢查會員基本資料顯示
3. ✅ 點擊「編輯」按鈕進入編輯模式
4. ✅ 修改姓名、電話、Email 等資料
5. ✅ 點擊「儲存」按鈕
6. ✅ 確認資料更新成功
7. ✅ 檢查「預約記錄」分頁
8. ✅ 檢查「票券記錄」分頁
9. ✅ 檢查「帳單記錄」分頁

**預期結果**:
- 會員詳情頁面正常載入
- 編輯功能正常工作
- 資料更新成功並顯示提示
- 各分頁內容正確顯示

### 測試 4: 會員資料編輯對話框
**路徑**: 在會員列表中點擊編輯按鈕

**測試步驟**:
1. ✅ 點擊會員列表中的「編輯」按鈕
2. ✅ 檢查編輯對話框是否正常彈出
3. ✅ 修改會員資料
4. ✅ 點擊「儲存」按鈕
5. ✅ 確認更新成功提示
6. ✅ 檢查列表中的資料是否已更新

**預期結果**:
- 編輯對話框正常顯示
- 表單預填現有資料
- 儲存功能正常工作
- 列表資料即時更新

## 🔍 資料庫驗證

### 檢查營業時間更新
```sql
-- 檢查營業時間記錄
SELECT * FROM business_hours 
WHERE shop_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'
ORDER BY day_of_week;
```

### 檢查會員資料更新
```sql
-- 檢查會員資料
SELECT user_id, display_name, email, phone, updated_at 
FROM profiles 
WHERE user_id = '{測試的用戶ID}'
ORDER BY updated_at DESC;
```

## 🚨 常見問題排除

### 營業時間設定問題
1. **如果仍出現重複鍵值錯誤**:
   - 檢查是否有多個相同的 shop_id 和 day_of_week 組合
   - 清理重複資料後重試

2. **如果設定無法保存**:
   - 檢查網路連線
   - 查看瀏覽器控制台錯誤
   - 確認管理員權限

### 會員編輯問題
1. **如果編輯功能無效**:
   - 檢查是否使用正確的 user_id
   - 確認 profiles 表的權限設定
   - 查看控制台錯誤訊息

2. **如果詳情頁面無法載入**:
   - 確認 URL 中的 user_id 正確
   - 檢查路由配置
   - 確認用戶資料存在

## ✅ 驗收標準

修正成功的標準：
- [ ] 營業時間設定不再出現重複鍵值錯誤
- [ ] 可以重複修改和儲存營業時間
- [ ] 會員列表正常顯示和搜尋
- [ ] 會員編輯對話框功能正常
- [ ] 會員詳情頁面正常載入和編輯
- [ ] 預約記錄和票券記錄正確顯示
- [ ] 所有更新操作都有成功提示
- [ ] 資料更新後立即反映在介面上

## 📝 測試報告模板

```
測試日期: ___________
測試環境: ___________
測試人員: ___________

營業時間設定測試:
□ 修改營業時間 - 通過/失敗
□ 儲存設定 - 通過/失敗  
□ 重複操作 - 通過/失敗

會員管理測試:
□ 會員列表載入 - 通過/失敗
□ 搜尋功能 - 通過/失敗
□ 編輯對話框 - 通過/失敗
□ 會員詳情頁 - 通過/失敗
□ 資料更新 - 通過/失敗

發現問題:
1. ___________
2. ___________

建議改進:
1. ___________
2. ___________
```

## 🎉 功能亮點

### 營業時間管理
- ✅ 解決重複鍵值約束問題
- ✅ 支援重複修改和儲存
- ✅ 資料一致性保證

### 會員管理
- ✅ 完整的會員資料編輯功能
- ✅ 詳細的會員資訊頁面
- ✅ 分頁顯示預約和票券記錄
- ✅ 即時資料更新和反饋

現在後台管理功能應該完全正常工作了！🚀
